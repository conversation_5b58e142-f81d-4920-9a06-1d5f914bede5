"use client";

import { useEffect } from "react";

const ProductReviews = () => {
  useEffect(() => {
    setupAccordion();
  }, []); // Add isReviewModalOpen as a dependency so it re-runs when modal state changes

  // Function to set up accordion functionality
  const setupAccordion = () => {
    const long_data = document.querySelectorAll(".log_des_acc_header");

    // First remove any existing event listeners to prevent duplicates
    long_data.forEach((header) => {
      const oldHeader = header.cloneNode(true);
      header?.parentNode?.replaceChild(oldHeader, header);
    });

    // Now add fresh event listeners
    document.querySelectorAll(".log_des_acc_header").forEach((header) => {
      header.addEventListener("click", function (e) {
        e.preventDefault();
        const targetId = this.getAttribute("data-target");
        if (!targetId) return;

        const targetElement = document.querySelector(targetId);
        const arrow = this.querySelector(".detail_arrow");

        if (!targetElement || !arrow) return;

        // Toggle the current section
        if (targetElement.classList.contains("newshow")) {
          targetElement.classList.remove("newshow");
          arrow.classList.remove("rotate");
        } else {
          // Close all other sections
          document.querySelectorAll(".newcollapse").forEach((collapse) => {
            collapse.classList.remove("newshow");
          });

          document.querySelectorAll(".detail_arrow").forEach((a) => {
            a.classList.remove("rotate");
          });

          // Open this section
          targetElement.classList.add("newshow");
          arrow.classList.add("rotate");
        }
      });
    });

    // Add the exact JS code as provided
    document
      .getElementById("toggle_desc_sub_heading")
      ?.addEventListener("click", () => {
        document
          .getElementById("toggle_desc_sub_paragraph")
          ?.classList.toggle("sub_desc_toggle_hidden");
      });
  };

  return (
    <div className="log_des_rating_box">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-normal m-0 rating-overview-heading">
          RATING OVERVIEW
        </h2>
        <button
          className="add_review_btn"
          onClick={(e) => {
            e.stopPropagation();
            setIsReviewModalOpen(true);
          }}
        >
          WRITE A REVIEW
        </button>
      </div>

      {product.reviews && product.reviews.length > 0 ? (
        <>
          <h4 className="overall-rating-heading m-0">Overall Rating</h4>
          <div className="flex items-center gap-4 mb-6">
            <span className="text-7xl font-bold leading-none main_rating">
              {product.rating}
            </span>
            <div>
              <div className="flex mb-1">
                <StarRating rating={product.rating} starSize={24} />
              </div>
              <p className="text-gray-600 m-0">
                Based on {product.reviews.length}{" "}
                {product.reviews.length === 1 ? "review" : "reviews"}
              </p>
            </div>
          </div>

          <h3 className="reviews-heading">REVIEWS</h3>

          <div className="log_des_review_list">
            {product.reviews
              .slice()
              .reverse()
              .map((review) => (
                <div
                  key={review.id}
                  className="flex items-start gap-4 log_des_single_review"
                >
                  <figure>
                    <img src="https://static.tossdown.com/site/0b4566ce-b249-448f-883b-f9f2e0aa5a96.webp" />
                    <figcaption>
                      <h5> {review.user_name} </h5>
                      <span> {review.date} </span>
                    </figcaption>
                  </figure>
                  <div className="log_des_single_review_data">
                    <div className="log_des_single_review_stars">
                      <StarRating rating={review.rating} starSize={20} />
                    </div>
                    <p> {review.comment} </p>
                  </div>
                </div>
              ))}
          </div>
        </>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">
            No reviews yet. Be the first to review this product!
          </p>
        </div>
      )}
    </div>
  );
};

export default ProductReviews;
