"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faChevronRight } from "@fortawesome/free-solid-svg-icons";

import { BreadCrumbProps } from "../index.types";
import { generateBreadCrumb } from "@/lib/utils/helperFunctions";

const BreadCrumb = ({
  items,
  productName,
  className = "",
}: BreadCrumbProps) => {
  const pathname = usePathname();

  // generate breadCrumb if not provided
  const breadcrumbs = items || generateBreadCrumb(pathname, productName);

  // ==============================|| UI ||============================== //

  // return null if no breadcrumbs to show
  if (!breadcrumbs?.length) {
    return null;
  }

  return (
    <nav aria-label="Breadcrumb" className={`text-sm mb-6 ${className}`}>
      <ol className="flex items-center flex-wrap">
        {breadcrumbs?.map((item, index) => (
          <li key={item?.href || index} className="flex items-center">
            {index > 0 && (
              <FontAwesomeIcon
                icon={faChevronRight}
                className="mx-2 text-gray-400 !w-3 !h-3"
              />
            )}

            {/* not clickable if isCurrent is true */}
            {item.isCurrent ? (
              <span className="font-medium text-gray-800" aria-current="page">
                {item.label}
              </span>
            ) : (
              <Link
                href={item?.href}
                className="text-gray-600 hover:text-gray-900 hover:underline"
              >
                {item?.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default BreadCrumb;
