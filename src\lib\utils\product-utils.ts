/**
 * Generates a URL-friendly slug from a product name and ID
 */
export function generateProductSlug(name: string, id: number | string): string {
  // Convert the name to lowercase, replace spaces with hyphens, and remove special characters
  const nameSlug = name
    .toLowerCase()
    .replace(/[^\w\s-]/g, "") // Remove special characters
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-") // Replace multiple hyphens with a single hyphen
    .trim();

  // Combine the name slug with the ID
  return `${nameSlug}-${id}`;
}
