import type { Metadata } from "next";

import { B<PERSON><PERSON>rumb, ProductDetails } from "@/app/components";

import { PageSEODetails } from "@/types";
import { callProductsDetailsAPI, getPageSEOData } from "@/lib/apiConfigs";
import { createMetadata, extractProductId } from "@/lib/utils/helperFunctions";

// generate metadata for the menu page (SSR)
export const generateMetadata = async (): Promise<Metadata> => {
  // function to call SEO API to fetch page SEO data
  const seoDetails: PageSEODetails = await getPageSEOData("detail");

  // function call to create Next.js metadata from SEO data
  return createMetadata(seoDetails);
};

// product detail page component
const Product = async ({ params }: { params: Promise<{ slug: string }> }) => {
  // ==============================|| API calls ||============================== //

  // function to call SEO API to fetch page h1
  const seoDetails: PageSEODetails = await getPageSEOData("detail");

  // get the route params
  const { slug } = await params;

  // extract product ID from slug
  const productId: string = extractProductId(slug);

  // function to call product details API
  const { data, error } = await callProductsDetailsAPI(productId);

  // ==============================|| UI ||============================== //

  return (
    <main>
      <section className="static_container mx-auto py-8 md:py-12">
        {/* hidden h1 for SEO */}
        <h1 className="sr-only">{seoDetails?.h1}</h1>

        {/* breadCrumb */}
        <BreadCrumb productName={data?.[0]?.name} />

        {/* error message */}
        {!!error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto mb-8">
            <p className="text-red-600 text-center">
              Unable to load product details at the moment. Please try
              refreshing the page.
            </p>
          </div>
        )}

        {/* product details */}
        {!error && <ProductDetails product={data?.[0] || []} />}
      </section>
    </main>
  );
};

export default Product;
