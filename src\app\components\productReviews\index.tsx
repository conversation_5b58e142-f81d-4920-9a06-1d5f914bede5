"use client";

import { useEffect } from "react";
import Image from "next/image";

import { Rating } from "@/app/components";
import { type ProductReviews, ProductReviewUser } from "@/types";
import { ProductReviewsProps } from "../index.types";

const ProductReviews: React.FC<ProductReviewsProps> = ({ reviews }) => {
  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function call to setup accordion functionality
    setUpAccordion();
  }, [reviews]);

  // ==============================|| handler functions ||============================== //

  /**
   * function to setup accordion functionality for product description
   * implemented here because it's a client side component
   */
  const setUpAccordion = (): void => {
    document.addEventListener("click", (event) => {
      const header = (event?.target as HTMLElement)?.closest(
        ".log_des_acc_header"
      );

      // not a accordion header click
      if (!header) return;

      event?.preventDefault();

      // get the target element and arrow
      const targetId = header?.getAttribute("data-target");

      // return if no target id
      if (!targetId) {
        return;
      }

      // get the target element and arrow
      const targetElement = document?.querySelector(targetId);

      // get the arrow element
      const arrow = header?.querySelector(".detail_arrow");

      // return if no target element or arrow
      if (!targetElement || !arrow) {
        return;
      }

      // check if the target element is already open
      const isOpen = targetElement?.classList?.contains("newshow");

      // close all sections
      document?.querySelectorAll(".newcollapse")?.forEach((collapse) => {
        collapse?.classList?.remove("newshow");
      });
      document?.querySelectorAll(".detail_arrow")?.forEach((icon) => {
        icon?.classList?.remove("rotate");
      });

      // open the target element
      if (!isOpen) {
        targetElement?.classList?.add("newshow");
        arrow?.classList?.add("rotate");
      }
    });

    // get the sub heading and paragraph
    const subHeading = document?.getElementById("toggle_desc_sub_heading");
    const subParagraph = document?.getElementById("toggle_desc_sub_paragraph");

    // return if no sub heading or paragraph
    if (!subHeading || !subParagraph) {
      return;
    }

    // add event listener to sub heading
    subHeading?.addEventListener("click", () => {
      subParagraph?.classList?.toggle("sub_desc_toggle_hidden");
    });
  };

  // ==============================|| UI ||============================== //

  return (
    <div className="log_des_rating_box">
      <div className="flex justify-between items-center mb-6">
        <h2 className="font-heading text-2xl font-normal m-0 rating-overview-heading">
          RATING OVERVIEW
        </h2>
      </div>

      {(reviews as ProductReviews)?.users?.length ? (
        <>
          <h4 className="overall-rating-heading m-0">Overall Rating</h4>

          <div className="flex items-center gap-4 mb-6">
            <span className="text-7xl font-bold leading-none main_rating">
              {(reviews as ProductReviews)?.rating}
            </span>

            <div>
              <div className="flex mb-1">
                <Rating
                  rating={parseFloat((reviews as ProductReviews)?.rating) || 0}
                  size="large"
                />
              </div>

              <p className="text-gray-600 m-0">
                Based on {(reviews as ProductReviews)?.total_reviews}{" "}
                {(reviews as ProductReviews)?.total_reviews === 1
                  ? "review"
                  : "reviews"}
              </p>
            </div>
          </div>

          <h3 className="reviews-heading">REVIEWS</h3>

          {/* users with their review */}
          <div className="log_des_review_list">
            {(reviews as ProductReviews)?.users
              ?.slice()
              ?.reverse()
              ?.map((review: ProductReviewUser) => (
                <div
                  key={review?.reviewid}
                  className="flex items-start gap-4 log_des_single_review"
                >
                  <figure>
                    <Image
                      src="https://static.tossdown.com/site/0b4566ce-b249-448f-883b-f9f2e0aa5a96.webp"
                      alt="User avatar"
                      width={48}
                      height={48}
                      className="w-12 h-12 rounded-full object-cover"
                    />

                    <figcaption>
                      <h5>{review?.user_name}</h5>
                      <span>{review?.date}</span>
                    </figcaption>
                  </figure>

                  <div className="log_des_single_review_data">
                    <div className="log_des_single_review_stars">
                      <Rating
                        rating={parseFloat(review?.rating) || 0}
                        size="small"
                      />
                    </div>

                    <p>{review?.comment}</p>
                  </div>
                </div>
              ))}
          </div>
        </>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">
            No reviews yet. Be the first to review this product!
          </p>
        </div>
      )}
    </div>
  );
};

export default ProductReviews;
