import axios from "@/lib/axios";
import moment from "moment-timezone";

import {
  PageSEODetails,
  RequestConfig,
  ProductsItem,
  ProductsList,
  ProductReviews,
  RequestHelperResponse,
  NextResponseData,
  BusinessDetails,
  BusinessDetailsResponse,
  Branch,
  GetUserDistanceRequest,
  GetPickupSlotsRequest,
  CartAbstractedRequest,
  CartRequest,
  CartResponse,
  Cart,
  CartItem,
  ProductSEO,
} from "@/types";
import { defaultSEO, requestTimeout } from "@/lib/utils/constants";
import {
  parseXMLForSEOData,
  getAttributeValue,
} from "@/lib/utils/helperFunctions";

// ==============================|| API endPoints ||============================== //

// get page SEO
export const getPageSEO = (): RequestConfig => ({
  url: process.env.SEO_FILE_URL || "",
  method: "GET",
  timeout: requestTimeout,
});

// get products
export const getProducts = (branchId?: string): RequestConfig => ({
  url: "/products",
  method: "GET",
  params: {
    business_id: process.env.NEXT_PUBLIC_BUSINESS_ID,
    branch_id: branchId || "",
    display_source: 2,
    attributes: 1,
  },
  timeout: requestTimeout,
});

// get product details
export const getProductDetails = (productId?: string): RequestConfig => ({
  url: "/product_details",
  method: "GET",
  params: {
    business_id: process.env.NEXT_PUBLIC_BUSINESS_ID,
    item_id: productId,
  },
  timeout: requestTimeout,
});

// get business details
export const getBusinessDetails = (): RequestConfig => ({
  baseURL: process.env.NEXT_PUBLIC_BUSINESS,
  url: `/${process.env.NEXT_PUBLIC_BUSINESS_ID}/locations`,
  method: "GET",
  timeout: requestTimeout,
});

// get user distance
export const getUserDistance = (): RequestConfig => ({
  url: "/get_order_delivery_areas",
  method: "POST",
  timeout: requestTimeout,
});

// get pickup slots
export const getPickupSlots = (
  params: GetPickupSlotsRequest
): RequestConfig => ({
  url: "/pickup_hours_time_slots",
  method: "GET",
  params,
  timeout: requestTimeout,
});

// cart
export const cart = (): RequestConfig => ({
  baseURL: process.env.NEXT_PUBLIC_CART,
  url: `/business/${process.env.NEXT_PUBLIC_BUSINESS_ID}/cart`,
  method: "POST",
  timeout: requestTimeout,
});

// ==============================|| API abstracted endPoints ||============================== //

// get abstracted products
export const getAbstractedProducts = (): RequestConfig => ({
  url: `${window.location.origin}/api/products`,
  method: "GET",
  timeout: requestTimeout,
});

// get abstracted business details
export const getAbstractedBusinessDetails = (): RequestConfig => ({
  url: `${window.location.origin}/api/businesses`,
  method: "GET",
  timeout: requestTimeout,
});

// get abstracted user distance
export const getAbstractedUserDistance = (
  data: GetUserDistanceRequest
): RequestConfig => ({
  url: `${window.location.origin}/api/distances`,
  method: "POST",
  data,
  timeout: requestTimeout,
});

// get abstracted pickup slots
export const getAbstractedPickupSlots = (
  params: GetPickupSlotsRequest
): RequestConfig => ({
  url: `${window.location.origin}/api/slots`,
  method: "GET",
  params,
  timeout: requestTimeout,
});

// abstracted cart
export const abstractedCart = (
  payload: CartAbstractedRequest
): RequestConfig => ({
  url: `${window.location.origin}/api/cart`,
  method: "POST",
  data: payload,
  timeout: requestTimeout,
});

// ==============================|| API payloads ||============================== //

// get user distance API payload
export const getUserDistanceAPIPayload = (
  payload: GetUserDistanceRequest
): FormData => {
  const formData: FormData = new FormData();

  formData.append("eatout_id", payload?.businessDetails?.businessId);
  formData.append("bid", payload?.businessDetails?.branchId);
  formData.append("lat", payload?.userDetails?.userLocation?.lat?.toString());
  formData.append("lng", payload?.userDetails?.userLocation?.lng?.toString());
  formData.append("postal_code", payload?.userDetails?.postalCode);
  formData.append("api_key", process.env.NEXT_PUBLIC_GOOGLE_MAPS_APP_KEY || "");
  formData.append("weight", "");

  return formData;
};

// cart API payload
export const cartAPIPayload = (payload: CartAbstractedRequest): CartRequest => {
  return {
    business_id: payload?.businessId,
    branch_id: payload?.branchId,
    action: payload?.action,
    unique_order_id: payload?.orderCartId,
    current_date: moment.utc().toISOString(),
    order_type: payload?.orderType,
    items: [
      {
        id: payload?.product?.id,
        image: payload?.product?.image,
        name: payload?.product?.name,
        price: payload?.product?.price,
        qty: 1,

        category_id: payload?.product?.categoryId || "0",
        category_name: payload?.product?.category || "",
        brand_id: "0",

        discount: 0,
        item_level_discount_value: 0,

        tax: "0",
        item_level_tax_value: 0,

        weight_value: "0",
        weight_unit: "kg",

        comment: "",
        product_code: "0",
        options: {},
      },
    ],
  };
};

// ==============================|| API calls ||============================== //

// function to get page SEO data
export const getPageSEOData = async (
  pageName: string
): Promise<PageSEODetails> => {
  try {
    // API call to fetch XML data containing SEO information
    const { data } = await axios(getPageSEO());

    // parse the XML data to extract SEO information for the specified page
    const pageSEODetails: PageSEODetails | null = parseXMLForSEOData(
      data,
      pageName
    );

    // return the SEO data
    return pageSEODetails || defaultSEO;

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    // if there's an error fetching or parsing the XML, return default SEO data
    return defaultSEO;
  }
};

// function to call get products API
export const callProductsAPI = async (
  branchId?: string
): Promise<RequestHelperResponse> => {
  try {
    // products list
    let productsList: ProductsList[] = [];

    // API call to get products
    const { data } = await axios(getProducts(branchId));

    // verify API response
    if (data?.status === "1" && data?.items?.length) {
      // function to handle the products API response
      productsList = handleProductsResponse(data?.items);
    }

    // return success response
    return { data: productsList, error: null };
  } catch (error) {
    // return error response
    return { data: [], error };
  }
};

// function to call get product details API
export const callProductsDetailsAPI = async (
  productId: string
): Promise<RequestHelperResponse> => {
  try {
    // products list
    let productsList: ProductsList[] = [];

    // API call to get product details
    const { data } = await axios(getProductDetails(productId));

    // verify API response
    if (data?.items?.length) {
      // function to handle the product details API response
      productsList = handleProductsResponse(data?.items);
    }

    // return success response
    return { data: productsList, error: null };
  } catch (error) {
    // return error response
    return { data: [], error };
  }
};

// ==============================|| Next.js abstracted API calls ||============================== //

// function to call get products API abstracted from Next.js
export const callProductsAbstractedAPI =
  async (): Promise<NextResponseData> => {
    try {
      // API call to fetch products data from internal API route
      const { data } = await axios(getAbstractedProducts());

      // return success response
      return data;
    } catch (error) {
      // return error response
      return { data: [], meta: null, error };
    }
  };

// function to call get business details API abstracted from Next.js
export const callBusinessDetailsAbstractedAPI =
  async (): Promise<NextResponseData> => {
    try {
      // API call to fetch business details data from internal API route
      const { data } = await axios(getAbstractedBusinessDetails());

      // return success response
      return data;
    } catch (error) {
      // return error response
      return { data: null, meta: null, error };
    }
  };

// function to call get user distance API abstracted from Next.js
export const callUserDistanceAbstractedAPI = async (
  addressData: GetUserDistanceRequest
): Promise<NextResponseData> => {
  try {
    // API call to validate user address through internal API route
    const { data } = await axios(getAbstractedUserDistance(addressData));

    // return success response
    return data;
  } catch (error) {
    // return error response
    return { data: null, meta: null, error };
  }
};

// function to call get pickup slots API abstracted from Next.js
export const callPickupSlotsAbstractedAPI = async (
  params: GetPickupSlotsRequest
): Promise<NextResponseData> => {
  try {
    // API call to fetch pickup slots from internal API route
    const { data } = await axios(getAbstractedPickupSlots(params));

    // return success response
    return data;
  } catch (error) {
    // return error response
    return { data: null, meta: null, error };
  }
};

// function to call cart API abstracted from Next.js
export const callCartAbstractedAPI = async (
  payload: CartAbstractedRequest
): Promise<NextResponseData> => {
  try {
    // API call to perform cart operations through internal API route
    const { data } = await axios(abstractedCart(payload));

    // return success response
    return data;
  } catch (error) {
    // return error response
    return { data: null, meta: null, error };
  }
};

// ==============================|| API responses ||============================== //

// function to map the business details API response to hide actual API response
export const handleBusinessesResponse = (
  businessDetails: BusinessDetailsResponse
): BusinessDetails => {
  return {
    name: businessDetails?.name,
    currency: businessDetails?.currencycode,
    minSpend: businessDetails?.minimum_spend,
    inventory: businessDetails?.inventory,
    contact: businessDetails?.contact_phone,
    email: businessDetails?.email,
    decimalPlaces: businessDetails?.decimal_places,
    addressKey: businessDetails?.address_api,
    locations: businessDetails?.branches?.map((branch: Branch) => ({
      id: branch?.id?.toString() || "",
      address: branch?.address,
      location: branch?.location,
      city: branch?.city,
      country: branch?.country,
      email: branch?.email,
      delivery: branch?.delivery,
      pickup: branch?.pickup,
      lat: branch?.lat,
      lng: branch?.lng,
      timeZone: branch?.time_zone,
      deliverySettings: branch?.delivery_settings || "",
    })),
  };
};

// function to map the products API response to hide actual API response
export const handleProductsResponse = (
  products: ProductsItem[]
): ProductsList[] => {
  // map over the products and return the required data
  return products?.map((item: ProductsItem) => {
    // type assertion for product details specific fields (using same handler for product & product detail APIs)
    const productDetails = item as ProductsItem & {
      product_reviews?: ProductReviews;
      seo?: ProductSEO;
    };

    return {
      id: item?.menu_item_id,
      name: item?.name,
      price: item?.price,
      currency: item?.currency,
      image: item?.image,
      description: item?.desc,
      categoryId: item?.menu_cat_id,
      category: item?.category,
      inStock: item?.status === "0",
      featured: item?.featured === "1",
      rating: item?.product_rating || "",
      attributes: getAttributeValue(item?.attributes),
      inventory: item?.inv_limit,

      // additional product detail specific fields
      reviews: productDetails?.product_reviews || [],
      seo: productDetails?.seo || null,
    };
  });
};

// function to map the cart API response to hide actual API response
export const handleCartResponse = (
  cart: CartResponse,
  product: ProductsList | CartItem,
  cartDetails: Cart
): Cart => {
  // map over the cart and return the required data
  const { items, itemsCount } = (cart?.items || [])?.reduce(
    (acc, item) => {
      const quantity: number = Number(item?.dqty) || 0;

      // skip items with 0 quantity (remove from cart)
      if (!quantity) {
        return acc;
      }

      // check if this item matches the provided product
      const currentProduct: boolean = item?.menu_item_id === product?.id;

      // get existing item data to preserve images and other details
      const currentCartItem: CartItem =
        cartDetails?.items?.[item?.menu_item_id];

      // add item to cart as key-value pair with product details
      acc.items[item?.menu_item_id] = {
        id: item?.menu_item_id,
        name: item?.dname,
        price: item?.dprice?.toString(),
        quantity,
        currency: currentProduct
          ? product?.currency
          : currentCartItem?.currency || "CAD",
        image: currentProduct ? product?.image : currentCartItem?.image || "",
        inventory: currentProduct
          ? product?.inventory
          : currentCartItem?.inventory || "0",
        categoryId: item?.category_id || "0",
        category: item?.category_name || "",
      };

      // update total quantity
      acc.itemsCount += quantity;

      return acc;
    },
    { items: {} as Cart["items"], itemsCount: 0 }
  );

  return {
    subTotal: cart?.total?.toString() || "0",
    itemsCount,
    items,
  };
};
