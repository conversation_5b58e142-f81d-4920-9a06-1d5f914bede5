import { ButtonHTMLAttributes } from "react";
import {
  AddressDetails,
  BreadCrumb,
  DropDownList,
  Locations,
  ProductsList,
  RequestStateData,
  ScheduleTypesOptions,
} from "@/types";

export interface SkeletonProps {
  rows?: number;
}

export interface NavigationMenuProps {
  className: string;
  onNavigate?: () => void;
  displayIcon?: boolean;
}

export interface MenuProps {
  products: ProductsList[];
  enableCart: boolean;
}

export interface ProductCardProps {
  product: ProductsList;
  enableCart?: boolean;
}

export interface RatingProps {
  rating: number;
  size?: "small" | "medium" | "large";
}

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?:
    | "primary"
    | "secondary"
    | "outline"
    | "ghost"
    | "danger"
    | "success";
  size?: "small" | "medium" | "large";
  loading?: boolean;
}

export interface SelectProps {
  title: string;
  placeholder?: string;
  value?: string;
  options: DropDownList[] | ScheduleTypesOptions[];
  onChange: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  className?: string;
  disabled?: boolean;
  icon?: React.ReactNode;
}

export interface IconProps {
  className?: string;
  size?: number;
}

export interface FieldErrorProps {
  errorMessage: string;
}

export interface SelectedLocationState {
  location: Locations | null;
  error: string;
}

export interface ScheduleTypeState {
  type: string;
  error: string;
}

export interface ScheduleState {
  date: DropDownList;
  time: string;
}

export interface ScheduleModalProps {
  scheduleModalCallback: (navigate?: boolean) => void;
}

export interface DeliveryAddressProps {
  deliveryAddressState: RequestStateData<AddressDetails>;
  setDeliveryAddressState: React.Dispatch<
    React.SetStateAction<RequestStateData<AddressDetails>>
  >;
  setSelectedLocation: React.Dispatch<
    React.SetStateAction<SelectedLocationState>
  >;
}

export interface PickupAddressProps {
  selectedLocation: SelectedLocationState;
  setSelectedLocation: React.Dispatch<
    React.SetStateAction<SelectedLocationState>
  >;
}

export interface ScheduleTypeProps {
  scheduleType: ScheduleTypeState;
  setScheduleType: React.Dispatch<React.SetStateAction<ScheduleTypeState>>;
  orderType: string;
}

export interface TabsProps {
  options: (number | string)[];
  value: number | string;
  size?: "small" | "medium" | "large";
  className?: string;
  tabClassName?: string;
  onClick?: (value: number | string) => void;
}

export interface ConfirmationModalProps {
  open: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
}

export interface BusinessContactProps {
  type?: "email" | "phone";
}

export interface ProductDetailProps {
  product: ProductsList;
}

export interface BreadCrumbProps {
  items?: BreadCrumb[];
  productName?: string;
  className?: string;
}
