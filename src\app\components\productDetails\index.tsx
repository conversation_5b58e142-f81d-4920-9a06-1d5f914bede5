import Image from "next/image";
import Link from "next/link";

import Rating from "@/app/components/rating";
import { ProductReviews } from "@/app/components";

import "@/styles/detailPage.styles.css";
import { ProductDetailProps } from "@/app/components/index.types";
import {
  extractProductDescription,
  getFormattedPrice,
} from "@/lib/utils/helperFunctions";
import { decimalPlaces, productPlaceholder } from "@/lib/utils/constants";

const ProductDetails = ({ product }: ProductDetailProps) => {
  // verify if product exists
  if (!product) {
    return null;
  }

  return (
    <div className={"pro_model_scroll"}>
      <div className="flex flex-col md:flex-row gap-8 mb-8 pop_box_1">
        {/* product image */}
        <div className="w-full md:w-1/2">
          <div className="relative aspect-square rounded-lg overflow-hidden">
            <Image
              src={product?.image || productPlaceholder}
              alt={product?.name}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, 50vw"
              priority
            />
          </div>
        </div>

        {/* product info */}
        <div className="w-full md:w-1/2 product_detail_page_headings">
          <div>
            <h2 className="text-3xl font-bold mb-2 uppercase">
              {product?.name}
            </h2>

            <div className="pop_header">
              {/* ratings */}
              <div className="flex items-center">
                <Rating rating={parseFloat(product?.rating) || 0} />
              </div>

              {/* price */}
              <h6 className="text-lg font-bold">
                {getFormattedPrice(
                  parseFloat(product?.price) || 0,
                  product?.currency,
                  decimalPlaces
                )}
              </h6>
            </div>
          </div>

          {/* order now link */}
          <div className="mt-7 md:mt-16">
            <Link
              href="/our-menu"
              className="text-[18px] md:text-[20px] font-normal leading-[20px] tracking-[0em] inline-flex items-center justify-center px-6 py-3 rounded-[2px] capitalize bg-black text-white cursor-pointer !no-underline"
            >
              ORDER NOW
            </Link>
          </div>
        </div>
      </div>

      {/* product description */}
      <div
        dangerouslySetInnerHTML={{
          __html: extractProductDescription(product?.description),
        }}
      />

      {/* product reviews */}
      <ProductReviews reviews={product?.reviews || []} />
    </div>
  );
};

export default ProductDetails;
